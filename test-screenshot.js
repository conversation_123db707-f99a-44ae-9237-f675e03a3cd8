const ScreenshotTool = require('./screenshot-tool');

class TestScreenshotTool extends ScreenshotTool {
    async readUrls() {
        // Override to test with just first 3 URLs
        const allUrls = await super.readUrls();
        return allUrls.slice(0, 3); // Test with first 3 URLs only
    }
}

async function testRun() {
    const tool = new TestScreenshotTool();
    
    try {
        console.log('Running test with first 3 URLs...');
        await tool.initialize();
        await tool.processAllUrls();
        console.log('\nTest completed successfully!');
    } catch (error) {
        console.error('Test failed:', error.message);
        process.exit(1);
    } finally {
        await tool.cleanup();
    }
}

testRun();
