const ScreenshotTool = require('./screenshot-tool');

class BatchScreenshotTool extends ScreenshotTool {
    constructor(options = {}) {
        super();
        this.batchSize = options.batchSize || 5; // Process 5 URLs at a time
        this.startIndex = options.startIndex || 0;
        this.endIndex = options.endIndex || null;
        this.pauseBetweenBatches = options.pauseBetweenBatches || 5000; // 5 second pause
    }

    async processAllUrls() {
        try {
            const allUrls = await this.readUrls();
            const endIdx = this.endIndex || allUrls.length;
            const urls = allUrls.slice(this.startIndex, endIdx);
            
            console.log(`Processing URLs ${this.startIndex + 1} to ${Math.min(endIdx, allUrls.length)} of ${allUrls.length} total`);
            console.log(`Batch size: ${this.batchSize}, Pause between batches: ${this.pauseBetweenBatches}ms`);

            // Clear previous log
            try {
                await this.logResult('', '', `Batch session started at ${new Date().toISOString()}`);
                await this.logResult('', '', `Processing URLs ${this.startIndex + 1}-${Math.min(endIdx, allUrls.length)}`);
            } catch (error) {
                console.error('Failed to initialize log file:', error.message);
            }

            // Process in batches
            for (let i = 0; i < urls.length; i += this.batchSize) {
                const batch = urls.slice(i, i + this.batchSize);
                const batchNum = Math.floor(i / this.batchSize) + 1;
                const totalBatches = Math.ceil(urls.length / this.batchSize);
                
                console.log(`\n=== BATCH ${batchNum}/${totalBatches} ===`);
                
                for (let j = 0; j < batch.length; j++) {
                    const url = batch[j];
                    const globalIndex = this.startIndex + i + j;
                    const filename = this.generateFilename(url, globalIndex);
                    
                    console.log(`\nProcessing ${globalIndex + 1}/${allUrls.length}: ${url}`);
                    await this.takeScreenshot(url, filename);
                    
                    // Small delay between screenshots
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
                // Pause between batches (except for the last batch)
                if (i + this.batchSize < urls.length) {
                    console.log(`\nPausing ${this.pauseBetweenBatches/1000} seconds before next batch...`);
                    await new Promise(resolve => setTimeout(resolve, this.pauseBetweenBatches));
                }
            }

            console.log('\n✓ Batch processing completed!');
            console.log(`Screenshots saved in: ${this.screenshotsDir}/`);
            console.log(`Log file: ${this.logFile}`);
            
        } catch (error) {
            console.error('Error processing URLs:', error.message);
            throw error;
        }
    }
}

// Command line argument parsing
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {};
    
    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--batch-size':
                options.batchSize = parseInt(args[++i]);
                break;
            case '--start':
                options.startIndex = parseInt(args[++i]) - 1; // Convert to 0-based
                break;
            case '--end':
                options.endIndex = parseInt(args[++i]);
                break;
            case '--pause':
                options.pauseBetweenBatches = parseInt(args[++i]) * 1000; // Convert to ms
                break;
            case '--help':
                console.log(`
Usage: node batch-screenshot.js [options]

Options:
  --batch-size <n>    Number of URLs to process in each batch (default: 5)
  --start <n>         Start from URL number n (1-based index)
  --end <n>           End at URL number n (1-based index)
  --pause <seconds>   Pause between batches in seconds (default: 5)
  --help              Show this help message

Examples:
  node batch-screenshot.js                    # Process all URLs in batches of 5
  node batch-screenshot.js --batch-size 10    # Process in batches of 10
  node batch-screenshot.js --start 1 --end 10 # Process only URLs 1-10
  node batch-screenshot.js --start 11         # Process from URL 11 to end
  node batch-screenshot.js --pause 10         # 10 second pause between batches
`);
                process.exit(0);
                break;
        }
    }
    
    return options;
}

async function main() {
    const options = parseArgs();
    const tool = new BatchScreenshotTool(options);
    
    try {
        await tool.initialize();
        await tool.processAllUrls();
    } catch (error) {
        console.error('Fatal error:', error.message);
        process.exit(1);
    } finally {
        await tool.cleanup();
    }
}

if (require.main === module) {
    main();
}
