const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');

class ScreenshotTool {
    constructor() {
        this.browser = null;
        this.context = null;
        this.screenshotsDir = 'screenshots';
        this.logFile = 'screenshot-log.txt';
        this.maxRetries = 3;
        this.timeout = 30000; // 30 seconds
    }

    async initialize() {
        // Create screenshots directory if it doesn't exist
        try {
            await fs.mkdir(this.screenshotsDir, { recursive: true });
        } catch (error) {
            console.log('Screenshots directory already exists or error creating it:', error.message);
        }

        // Launch browser
        console.log('Launching browser...');
        this.browser = await chromium.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });

        this.context = await this.browser.newContext({
            viewport: { width: 1920, height: 1080 },
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        });
    }

    async readUrls() {
        try {
            const content = await fs.readFile('links.txt', 'utf-8');
            return content
                .split('\n')
                .map(line => line.trim())
                .filter(line => line && line.startsWith('http'));
        } catch (error) {
            throw new Error(`Failed to read links.txt: ${error.message}`);
        }
    }

    generateFilename(url, index) {
        try {
            const urlObj = new URL(url);
            let domain = urlObj.hostname.replace(/^www\./, '');
            
            // Clean domain name for filename
            domain = domain.replace(/[^a-zA-Z0-9.-]/g, '_');
            
            // Add index to ensure uniqueness
            const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
            return `${String(index + 1).padStart(3, '0')}_${domain}_${timestamp}.png`;
        } catch (error) {
            // Fallback to index-based naming if URL parsing fails
            return `screenshot_${String(index + 1).padStart(3, '0')}.png`;
        }
    }

    async takeScreenshot(url, filename, retryCount = 0) {
        const page = await this.context.newPage();
        
        try {
            console.log(`Taking screenshot of: ${url}`);
            
            // Set longer timeout for page load
            await page.goto(url, { 
                waitUntil: 'networkidle', 
                timeout: this.timeout 
            });

            // Wait for additional content to load
            await page.waitForTimeout(3000);

            // Handle cookie banners and popups
            try {
                // Common cookie banner selectors
                const cookieSelectors = [
                    '[id*="cookie"]',
                    '[class*="cookie"]',
                    '[id*="consent"]',
                    '[class*="consent"]',
                    '.cookie-banner',
                    '.cookie-notice',
                    '#cookie-notice'
                ];

                for (const selector of cookieSelectors) {
                    const elements = await page.$$(selector);
                    for (const element of elements) {
                        try {
                            await element.click({ timeout: 1000 });
                            await page.waitForTimeout(1000);
                        } catch (e) {
                            // Ignore click failures
                        }
                    }
                }
            } catch (error) {
                // Ignore cookie banner handling errors
            }

            // Scroll to bottom to trigger lazy loading
            await page.evaluate(async () => {
                await new Promise((resolve) => {
                    let totalHeight = 0;
                    const distance = 100;
                    const timer = setInterval(() => {
                        const scrollHeight = document.body.scrollHeight;
                        window.scrollBy(0, distance);
                        totalHeight += distance;

                        if (totalHeight >= scrollHeight) {
                            clearInterval(timer);
                            resolve();
                        }
                    }, 100);
                });
            });

            // Wait for images and other content to load
            await page.waitForTimeout(2000);

            // Scroll back to top
            await page.evaluate(() => window.scrollTo(0, 0));
            await page.waitForTimeout(1000);

            // Take full page screenshot
            const screenshotPath = path.join(this.screenshotsDir, filename);
            await page.screenshot({
                path: screenshotPath,
                fullPage: true,
                type: 'png'
            });

            console.log(`✓ Screenshot saved: ${filename}`);
            await this.logResult(url, filename, 'SUCCESS');
            
        } catch (error) {
            console.error(`✗ Failed to screenshot ${url}: ${error.message}`);
            
            if (retryCount < this.maxRetries) {
                console.log(`Retrying... (${retryCount + 1}/${this.maxRetries})`);
                await page.close();
                return this.takeScreenshot(url, filename, retryCount + 1);
            } else {
                await this.logResult(url, filename, `FAILED: ${error.message}`);
            }
        } finally {
            await page.close();
        }
    }

    async logResult(url, filename, status) {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] ${status} - ${url} -> ${filename}\n`;
        
        try {
            await fs.appendFile(this.logFile, logEntry);
        } catch (error) {
            console.error('Failed to write to log file:', error.message);
        }
    }

    async processAllUrls() {
        try {
            const urls = await this.readUrls();
            console.log(`Found ${urls.length} URLs to process`);

            // Clear previous log
            try {
                await fs.writeFile(this.logFile, `Screenshot session started at ${new Date().toISOString()}\n`);
            } catch (error) {
                console.error('Failed to initialize log file:', error.message);
            }

            for (let i = 0; i < urls.length; i++) {
                const url = urls[i];
                const filename = this.generateFilename(url, i);
                
                console.log(`\nProcessing ${i + 1}/${urls.length}: ${url}`);
                await this.takeScreenshot(url, filename);
                
                // Small delay between screenshots to be respectful to servers
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            console.log('\n✓ All screenshots completed!');
            console.log(`Screenshots saved in: ${this.screenshotsDir}/`);
            console.log(`Log file: ${this.logFile}`);
            
        } catch (error) {
            console.error('Error processing URLs:', error.message);
            throw error;
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
            console.log('Browser closed');
        }
    }
}

// Main execution
async function main() {
    const tool = new ScreenshotTool();
    
    try {
        await tool.initialize();
        await tool.processAllUrls();
    } catch (error) {
        console.error('Fatal error:', error.message);
        process.exit(1);
    } finally {
        await tool.cleanup();
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log('\nReceived SIGINT, cleaning up...');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\nReceived SIGTERM, cleaning up...');
    process.exit(0);
});

if (require.main === module) {
    main();
}

module.exports = ScreenshotTool;
