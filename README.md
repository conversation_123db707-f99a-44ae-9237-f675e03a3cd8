# News Screenshots Tool

An automated tool to take full-page screenshots of websites listed in `links.txt` using Playwright.

## Features

- **Full-page screenshots**: Captures entire webpage content, not just the viewport
- **Smart loading**: Handles dynamic content, lazy loading, and JavaScript-heavy sites
- **Error handling**: Robust retry mechanism for failed URLs
- **Cookie banner handling**: Automatically dismisses common cookie consent banners
- **Descriptive filenames**: Uses domain names and timestamps for easy identification
- **Comprehensive logging**: Tracks success/failure status for each URL
- **Respectful crawling**: Includes delays between requests to avoid overwhelming servers

## Installation

1. Install Node.js dependencies:
```bash
npm install
```

2. Install Playwright browsers:
```bash
npm run install-browsers
```

## Usage

1. Make sure your URLs are listed in `links.txt` (one URL per line)

2. Run the screenshot tool:
```bash
npm run screenshot
```

Or directly with Node.js:
```bash
node screenshot-tool.js
```

## Output

- **Screenshots**: Saved in `screenshots/` directory as PNG files
- **Naming format**: `001_domain.com_2025-09-14.png`
- **Log file**: `screenshot-log.txt` contains detailed execution log

## Configuration

The tool includes several configurable options in the `ScreenshotTool` class:

- `timeout`: Page load timeout (default: 30 seconds)
- `maxRetries`: Number of retry attempts for failed URLs (default: 3)
- `viewport`: Browser viewport size (default: 1920x1080)

## Troubleshooting

### Common Issues

1. **Browser installation fails**:
   ```bash
   npx playwright install chromium
   ```

2. **Memory issues with large pages**:
   - The tool includes memory optimization flags
   - Consider processing URLs in smaller batches

3. **Network timeouts**:
   - Increase the timeout value in the script
   - Check your internet connection

4. **Permission errors**:
   - Ensure write permissions for the screenshots directory
   - Run with appropriate user permissions

### Error Handling

- Failed URLs are logged with error details
- The tool continues processing remaining URLs even if some fail
- Retry mechanism attempts failed URLs up to 3 times
- Network and loading issues are handled gracefully

## File Structure

```
news-shots/
├── links.txt              # Input URLs (one per line)
├── screenshot-tool.js     # Main automation script
├── package.json          # Node.js dependencies
├── screenshots/          # Output directory (created automatically)
├── screenshot-log.txt    # Execution log (created during run)
└── README.md            # This file
```

## Technical Details

- **Browser**: Chromium (headless mode)
- **Screenshot format**: PNG
- **Page loading**: Waits for network idle + additional buffer time
- **Scroll handling**: Automatically scrolls to trigger lazy loading
- **User agent**: Modern Chrome user agent string
- **Security**: Runs with sandbox disabled for compatibility

## Supported URL Types

The tool works with most web pages including:
- News websites
- Social media posts (Facebook, Instagram)
- Dynamic content sites
- Sites with lazy loading
- Mobile-responsive pages

Note: Some sites may block automated access or require special handling.
