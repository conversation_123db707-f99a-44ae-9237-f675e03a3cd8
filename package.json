{"name": "news-shots", "version": "1.0.0", "description": "Automated screenshot tool for news websites", "main": "screenshot-tool.js", "scripts": {"install-browsers": "npx playwright install", "screenshot": "node screenshot-tool.js", "screenshot-batch": "node batch-screenshot.js", "screenshot-test": "node test-screenshot.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["screenshot", "automation", "playwright", "news"], "author": "", "license": "MIT", "dependencies": {"playwright": "^1.40.0", "fs": "^0.0.1-security", "path": "^0.12.7"}}